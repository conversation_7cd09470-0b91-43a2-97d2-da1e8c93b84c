{"name": "frontend", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "build-only": "vite build", "type-check": "vue-tsc --build", "lint": "eslint . --fix", "format": "prettier --write src/"}, "dependencies": {"@tailwindcss/vite": "^4.1.7", "@tanstack/vue-table": "^8.21.3", "@types/js-cookie": "^3.0.6", "@vee-validate/zod": "^4.15.0", "@vueuse/core": "^13.3.0", "axios": "^1.9.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dayjs": "^1.11.13", "js-cookie": "^3.0.5", "lucide-vue-next": "^0.510.0", "maska": "^3.1.1", "pinia": "^3.0.1", "qrcode.vue": "^3.6.0", "reka-ui": "^2.3.0", "tailwind-merge": "^3.3.0", "tailwindcss": "^4.1.7", "tw-animate-css": "^1.2.9", "vee-validate": "^4.15.0", "vue": "^3.5.13", "vue-router": "^4.5.0", "vue-sonner": "^1.3.2", "zod": "^3.24.4"}, "devDependencies": {"@tsconfig/node22": "^22.0.1", "@types/node": "^22.15.18", "@vitejs/plugin-vue": "^5.2.3", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.5.0", "@vue/tsconfig": "^0.7.0", "eslint": "^9.22.0", "eslint-plugin-vue": "~10.0.0", "jiti": "^2.4.2", "npm-run-all2": "^7.0.2", "prettier": "3.5.3", "typescript": "~5.8.0", "vite": "^6.2.4", "vite-plugin-vue-devtools": "^7.7.2", "vue-tsc": "^2.2.8"}}