<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'
import { reactiveOmit } from '@vueuse/core'
import { ChevronDown } from 'lucide-vue-next'
import { SelectIcon, SelectTrigger, type SelectTriggerProps, useForwardProps } from 'reka-ui'

const props = withDefaults(
  defineProps<
    SelectTriggerProps & {
      class?: HTMLAttributes['class']
      size?: 'sm' | 'default'
      variant?: 'default' | 'chips'
    }
  >(),
  {
    size: 'default',
    variant: 'default',
  },
)

const delegatedProps = reactiveOmit(props, 'class', 'size', 'variant')
const forwardedProps = useForwardProps(delegatedProps)
</script>

<template>
  <SelectTrigger
    data-slot="select-trigger"
    :data-size="size"
    :data-variant="variant"
    v-bind="forwardedProps"
    :class="
      cn(
        // Base styles
        'border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*=\'text-\'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex rounded-md border bg-transparent text-sm shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*=\'size-\'])]:size-4',

        // Default variant styles
        variant === 'default' &&
          'w-fit items-center justify-between gap-2 px-3 py-2 whitespace-nowrap data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2',

        // Chips variant styles
        variant === 'chips' &&
          'w-full min-h-[2.25rem] items-start justify-between gap-2 px-3 py-2 data-[size=default]:min-h-[2.25rem] data-[size=sm]:min-h-[2rem] *:data-[slot=select-chips-value]:flex *:data-[slot=select-chips-value]:flex-wrap *:data-[slot=select-chips-value]:items-center *:data-[slot=select-chips-value]:gap-1',

        props.class,
      )
    "
  >
    <div class="flex-1 min-w-0">
      <slot />
    </div>
    <SelectIcon as-child>
      <ChevronDown class="size-4 opacity-50 shrink-0 mt-0.5" />
    </SelectIcon>
  </SelectTrigger>
</template>
