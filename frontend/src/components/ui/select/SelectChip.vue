<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { cn } from '@/lib/utils'
import { X } from 'lucide-vue-next'
import { Badge } from '@/components/ui/badge'

interface SelectChipProps {
  value: string | number
  label: string
  disabled?: boolean
  removable?: boolean
  class?: HTMLAttributes['class']
}

const props = withDefaults(defineProps<SelectChipProps>(), {
  disabled: false,
  removable: true,
})

const emits = defineEmits<{
  remove: [value: string | number]
}>()

const handleRemove = (event: Event) => {
  event.preventDefault()
  event.stopPropagation()
  if (!props.disabled) {
    emits('remove', props.value)
  }
}
</script>

<template>
  <Badge
    variant="secondary"
    :class="
      cn(
        'inline-flex items-center gap-1 pr-1 max-w-full',
        disabled && 'opacity-50 cursor-not-allowed',
        props.class,
      )
    "
  >
    <span class="truncate">{{ label }}</span>
    <button
      v-if="removable && !disabled"
      type="button"
      :class="
        cn(
          'ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2',
          'hover:bg-secondary-foreground/20 h-4 w-4 flex items-center justify-center',
        )
      "
      @click="handleRemove"
      @keydown.enter="handleRemove"
      @keydown.space="handleRemove"
    >
      <X class="h-3 w-3" />
      <span class="sr-only">Remove {{ label }}</span>
    </button>
  </Badge>
</template>
