<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { computed } from 'vue'
import { cn } from '@/lib/utils'
import { SelectChip } from '.'
import { injectSelectRootContext } from 'reka-ui'

interface SelectChipsValueProps {
  placeholder?: string
  maxDisplay?: number
  class?: HTMLAttributes['class']
  chipClass?: HTMLAttributes['class']
  options?: Array<{ value: any; label: string; [key: string]: any }>
  displayKey?: string // Key to use for display (e.g., 'name', 'title', 'label')
  valueKey?: string // Key to use for value comparison (defaults to 'value')
  getDisplayText?: (option: any, value: any) => string // Custom function to get display text
}

const props = withDefaults(defineProps<SelectChipsValueProps>(), {
  placeholder: '',
  maxDisplay: undefined,
  options: () => [],
  displayKey: 'label',
  valueKey: 'value',
  getDisplayText: undefined,
})

const emits = defineEmits<{
  removeValue: [value: string | number]
}>()

// Get the select context from reka-ui
const selectContext = injectSelectRootContext()

const selectedValues = computed(() => {
  if (!selectContext.modelValue?.value) return []
  const values = Array.isArray(selectContext.modelValue.value)
    ? selectContext.modelValue.value
    : [selectContext.modelValue.value]
  return values
})

const displayedChips = computed(() => {
  const chips = selectedValues.value.map((value: any) => {
    const option = props.options.find((opt) => opt[props.valueKey!] === value)

    let displayText: string

    if (props.getDisplayText) {
      // Use custom display function
      displayText = props.getDisplayText(option, value)
    } else if (option && props.displayKey && option[props.displayKey]) {
      // Use specified display key
      displayText = String(option[props.displayKey])
    } else if (option && option.label) {
      // Fallback to label
      displayText = String(option.label)
    } else {
      // Final fallback to value itself
      displayText = String(value)
    }

    return {
      value,
      label: displayText,
      option, // Include the full option object for advanced use cases
    }
  })

  if (props.maxDisplay && chips.length > props.maxDisplay) {
    return chips.slice(0, props.maxDisplay)
  }

  return chips
})

const remainingCount = computed(() => {
  if (!props.maxDisplay || selectedValues.value.length <= props.maxDisplay) {
    return 0
  }
  return selectedValues.value.length - props.maxDisplay
})

const hasValues = computed(() => selectedValues.value.length > 0)

const handleRemoveChip = (value: string | number) => {
  // Remove the value from the current selection
  const currentValues = Array.isArray(selectContext.modelValue.value)
    ? selectContext.modelValue.value
    : [selectContext.modelValue.value]

  const newValues = currentValues.filter((v: any) => v !== value)
  selectContext.onValueChange(newValues)
  emits('removeValue', value)
}
</script>

<template>
  <div
    data-slot="select-chips-value"
    :class="
      cn(
        'flex flex-wrap items-center gap-1 min-h-[1.5rem] w-full',
        !hasValues && 'text-muted-foreground',
        props.class,
      )
    "
  >
    <!-- Placeholder when no values selected -->
    <span v-if="!hasValues && placeholder" class="truncate">
      {{ placeholder }}
    </span>

    <!-- Display chips for selected values -->
    <template v-if="hasValues">
      <SelectChip
        v-for="chip in displayedChips"
        :key="chip.value"
        :value="chip.value"
        :label="chip.label"
        :option="chip.option"
        :class="chipClass"
        @remove="handleRemoveChip"
      >
        <!-- Custom slot for chip content -->
        <template #default="{ value, label, option, disabled }">
          <slot name="chip" :value="value" :label="label" :option="option" :disabled="disabled">
            <span class="truncate">{{ label }}</span>
          </slot>
        </template>
      </SelectChip>

      <!-- Show remaining count if there are more items -->
      <span
        v-if="remainingCount > 0"
        class="text-xs text-muted-foreground bg-muted px-2 py-1 rounded"
      >
        +{{ remainingCount }} more
      </span>
    </template>

    <!-- Fallback slot for custom content -->
    <slot
      :selected-values="selectedValues"
      :displayed-chips="displayedChips"
      :remaining-count="remainingCount"
      :has-values="hasValues"
    />
  </div>
</template>
