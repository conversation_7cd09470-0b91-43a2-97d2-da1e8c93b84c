<script setup lang="ts">
import type { HTMLAttributes } from 'vue'
import { computed } from 'vue'
import { cn } from '@/lib/utils'
import { SelectChip } from '.'
import { injectSelectRootContext } from 'reka-ui'

interface SelectChipsValueProps {
  placeholder?: string
  maxDisplay?: number
  class?: HTMLAttributes['class']
  chipClass?: HTMLAttributes['class']
  options?: Array<{ value: any; label: string }>
}

const props = withDefaults(defineProps<SelectChipsValueProps>(), {
  placeholder: '',
  maxDisplay: undefined,
  options: () => [],
})

const emits = defineEmits<{
  removeValue: [value: string | number]
}>()

// Get the select context from reka-ui
const selectContext = injectSelectRootContext()

const selectedValues = computed(() => {
  if (!selectContext.modelValue?.value) return []
  const values = Array.isArray(selectContext.modelValue.value)
    ? selectContext.modelValue.value
    : [selectContext.modelValue.value]
  return values
})

const displayedChips = computed(() => {
  const chips = selectedValues.value.map((value: any) => {
    const option = props.options.find((opt) => opt.value === value)
    return {
      value,
      label: option?.label || String(value),
    }
  })

  if (props.maxDisplay && chips.length > props.maxDisplay) {
    return chips.slice(0, props.maxDisplay)
  }

  return chips
})

const remainingCount = computed(() => {
  if (!props.maxDisplay || selectedValues.value.length <= props.maxDisplay) {
    return 0
  }
  return selectedValues.value.length - props.maxDisplay
})

const hasValues = computed(() => selectedValues.value.length > 0)

const handleRemoveChip = (value: string | number) => {
  // Remove the value from the current selection
  const currentValues = Array.isArray(selectContext.modelValue.value)
    ? selectContext.modelValue.value
    : [selectContext.modelValue.value]

  const newValues = currentValues.filter((v: any) => v !== value)
  selectContext.onValueChange(newValues)
  emits('removeValue', value)
}
</script>

<template>
  <div
    data-slot="select-chips-value"
    :class="
      cn(
        'flex flex-wrap items-center gap-1 min-h-[1.5rem] w-full',
        !hasValues && 'text-muted-foreground',
        props.class,
      )
    "
  >
    <!-- Placeholder when no values selected -->
    <span v-if="!hasValues && placeholder" class="truncate">
      {{ placeholder }}
    </span>

    <!-- Display chips for selected values -->
    <template v-if="hasValues">
      <SelectChip
        v-for="chip in displayedChips"
        :key="chip.value"
        :value="chip.value"
        :label="chip.label"
        :class="chipClass"
        @remove="handleRemoveChip"
      />

      <!-- Show remaining count if there are more items -->
      <span
        v-if="remainingCount > 0"
        class="text-xs text-muted-foreground bg-muted px-2 py-1 rounded"
      >
        +{{ remainingCount }} more
      </span>
    </template>

    <!-- Fallback slot for custom content -->
    <slot
      :selected-values="selectedValues"
      :displayed-chips="displayedChips"
      :remaining-count="remainingCount"
      :has-values="hasValues"
    />
  </div>
</template>
