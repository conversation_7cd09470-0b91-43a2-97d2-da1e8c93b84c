<script setup lang="ts">
import { ref } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectChipsValue,
} from '@/components/ui/select'
import { Label } from '@/components/ui/label'

// Demo data
const fruits = [
  { value: 'apple', label: 'Apple', emoji: '🍎', category: 'Tree Fruit' },
  { value: 'banana', label: 'Banana', emoji: '🍌', category: 'Tropical' },
  { value: 'orange', label: 'Orange', emoji: '🍊', category: 'Citrus' },
  { value: 'grape', label: 'Grape', emoji: '🍇', category: 'Berry' },
  { value: 'strawberry', label: 'Strawberry', emoji: '🍓', category: 'Berry' },
  { value: 'mango', label: 'Mango', emoji: '🥭', category: 'Tropical' },
  { value: 'pineapple', label: 'Pineapple', emoji: '🍍', category: 'Tropical' },
  { value: 'kiwi', label: 'Kiwi', emoji: '🥝', category: 'Exotic' },
  { value: 'watermelon', label: 'Watermelon', emoji: '🍉', category: 'Melon' },
  { value: 'blueberry', label: 'Blueberry', emoji: '🫐', category: 'Berry' },
]

// Complex data example (like products or users)
const products = [
  { id: 1, name: 'MacBook Pro', brand: 'Apple', price: 2499, category: 'Laptop' },
  { id: 2, name: 'iPhone 15', brand: 'Apple', price: 999, category: 'Phone' },
  { id: 3, name: 'Galaxy S24', brand: 'Samsung', price: 899, category: 'Phone' },
  { id: 4, name: 'ThinkPad X1', brand: 'Lenovo', price: 1899, category: 'Laptop' },
  { id: 5, name: 'Surface Pro', brand: 'Microsoft', price: 1299, category: 'Tablet' },
]

// Custom display function example
const getProductDisplayText = (option: any, value: any) => {
  if (!option) return String(value)
  return `${option.name} (${option.brand})`
}

// Reactive values
const singleValue = ref('')
const multipleValues = ref([])
const chipsValues = ref([])
const chipsWithLimitValues = ref([])
const chipsWithEmojiValues = ref([])
const productValues = ref([])
const customChipValues = ref([])
</script>

<template>
  <div class="container mx-auto p-6 space-y-8">
    <div class="space-y-2">
      <h1 class="text-3xl font-bold">Select Components Demo</h1>
      <p class="text-muted-foreground">
        Demonstrating the enhanced select component with chips variant for multiple selection.
      </p>
    </div>

    <div class="grid gap-6 lg:grid-cols-2">
      <!-- Single Selection -->
      <Card>
        <CardHeader>
          <CardTitle>Single Selection</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-2">
            <Label>Choose a fruit</Label>
            <Select v-model="singleValue">
              <SelectTrigger>
                <SelectValue placeholder="Select a fruit..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="fruit in fruits" :key="fruit.value" :value="fruit.value">
                  {{ fruit.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div class="text-sm text-muted-foreground">Selected: {{ singleValue || 'None' }}</div>
        </CardContent>
      </Card>

      <!-- Multiple Selection (Default) -->
      <Card>
        <CardHeader>
          <CardTitle>Multiple Selection (Default)</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-2">
            <Label>Choose multiple fruits</Label>
            <Select v-model="multipleValues" multiple>
              <SelectTrigger>
                <SelectValue placeholder="Select fruits..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="fruit in fruits" :key="fruit.value" :value="fruit.value">
                  {{ fruit.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div class="text-sm text-muted-foreground">
            Selected: {{ multipleValues.length ? multipleValues.join(', ') : 'None' }}
          </div>
        </CardContent>
      </Card>

      <!-- Multiple Selection with Chips -->
      <Card>
        <CardHeader>
          <CardTitle>Multiple Selection with Chips</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-2">
            <Label>Choose multiple fruits (chips variant)</Label>
            <Select v-model="chipsValues" multiple>
              <SelectTrigger variant="chips">
                <SelectChipsValue placeholder="Select fruits..." :options="fruits" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="fruit in fruits" :key="fruit.value" :value="fruit.value">
                  {{ fruit.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div class="text-sm text-muted-foreground">
            Selected: {{ chipsValues.length ? chipsValues.join(', ') : 'None' }}
          </div>
        </CardContent>
      </Card>

      <!-- Multiple Selection with Chips (Limited Display) -->
      <Card>
        <CardHeader>
          <CardTitle>Chips with Display Limit</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-2">
            <Label>Choose multiple fruits (max 3 chips shown)</Label>
            <Select v-model="chipsWithLimitValues" multiple>
              <SelectTrigger variant="chips">
                <SelectChipsValue
                  placeholder="Select fruits..."
                  :options="fruits"
                  :max-display="3"
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="fruit in fruits" :key="fruit.value" :value="fruit.value">
                  {{ fruit.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div class="text-sm text-muted-foreground">
            Selected: {{ chipsWithLimitValues.length ? chipsWithLimitValues.join(', ') : 'None' }}
          </div>
        </CardContent>
      </Card>

      <!-- Chips with Custom Display Key -->
      <Card>
        <CardHeader>
          <CardTitle>Custom Display Key (Emoji)</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-2">
            <Label>Choose fruits (showing emoji instead of label)</Label>
            <Select v-model="chipsWithEmojiValues" multiple>
              <SelectTrigger variant="chips">
                <SelectChipsValue
                  placeholder="Select fruits..."
                  :options="fruits"
                  display-key="emoji"
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="fruit in fruits" :key="fruit.value" :value="fruit.value">
                  {{ fruit.emoji }} {{ fruit.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div class="text-sm text-muted-foreground">
            Selected: {{ chipsWithEmojiValues.length ? chipsWithEmojiValues.join(', ') : 'None' }}
          </div>
        </CardContent>
      </Card>

      <!-- Complex Objects with Custom Display Function -->
      <Card>
        <CardHeader>
          <CardTitle>Complex Objects (Products)</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-2">
            <Label>Choose products (using custom display function)</Label>
            <Select v-model="productValues" multiple>
              <SelectTrigger variant="chips">
                <SelectChipsValue
                  placeholder="Select products..."
                  :options="products"
                  value-key="id"
                  :get-display-text="getProductDisplayText"
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="product in products" :key="product.id" :value="product.id">
                  {{ product.name }} - {{ product.brand }} (${{ product.price }})
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div class="text-sm text-muted-foreground">
            Selected IDs: {{ productValues.length ? productValues.join(', ') : 'None' }}
          </div>
        </CardContent>
      </Card>

      <!-- Custom Chip Content with Slot -->
      <Card>
        <CardHeader>
          <CardTitle>Custom Chip Content</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-2">
            <Label>Choose fruits (custom chip design)</Label>
            <Select v-model="customChipValues" multiple>
              <SelectTrigger variant="chips">
                <SelectChipsValue placeholder="Select fruits..." :options="fruits">
                  <template #chip="{ option, label }">
                    <div class="flex items-center gap-1">
                      <span>{{ option?.emoji }}</span>
                      <span class="font-medium">{{ label }}</span>
                      <span class="text-xs text-muted-foreground">({{ option?.category }})</span>
                    </div>
                  </template>
                </SelectChipsValue>
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="fruit in fruits" :key="fruit.value" :value="fruit.value">
                  {{ fruit.emoji }} {{ fruit.label }} - {{ fruit.category }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div class="text-sm text-muted-foreground">
            Selected: {{ customChipValues.length ? customChipValues.join(', ') : 'None' }}
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Code Examples -->
    <Card>
      <CardHeader>
        <CardTitle>Usage Examples</CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="space-y-2">
          <h4 class="font-medium">Basic Chips Usage:</h4>
          <pre
            class="bg-muted p-4 rounded-md text-sm overflow-x-auto"
          ><code>&lt;Select v-model="selectedValues" multiple&gt;
  &lt;SelectTrigger variant="chips"&gt;
    &lt;SelectChipsValue
      placeholder="Select options..."
      :options="options"
    /&gt;
  &lt;/SelectTrigger&gt;
  &lt;SelectContent&gt;
    &lt;SelectItem v-for="option in options" :value="option.value"&gt;
      {{ option.label }}
    &lt;/SelectItem&gt;
  &lt;/SelectContent&gt;
&lt;/Select&gt;</code></pre>
        </div>

        <div class="space-y-2">
          <h4 class="font-medium">2. Custom Display Key:</h4>
          <pre class="bg-muted p-4 rounded-md text-sm overflow-x-auto"><code>&lt;SelectChipsValue
  placeholder="Select options..."
  :options="options"
  display-key="name"  // Show 'name' instead of 'label'
/&gt;</code></pre>
        </div>

        <div class="space-y-2">
          <h4 class="font-medium">3. Complex Objects with Custom Value Key:</h4>
          <pre class="bg-muted p-4 rounded-md text-sm overflow-x-auto"><code>&lt;SelectChipsValue
  placeholder="Select products..."
  :options="products"
  value-key="id"      // Use 'id' for value comparison
  display-key="name"  // Show 'name' in chips
/&gt;</code></pre>
        </div>

        <div class="space-y-2">
          <h4 class="font-medium">4. Custom Display Function:</h4>
          <pre
            class="bg-muted p-4 rounded-md text-sm overflow-x-auto"
          ><code>const getDisplayText = (option, value) => {
  return option ? `${option.name} (${option.brand})` : String(value)
}

&lt;SelectChipsValue
  :options="products"
  value-key="id"
  :get-display-text="getDisplayText"
/&gt;</code></pre>
        </div>

        <div class="space-y-2">
          <h4 class="font-medium">5. Custom Chip Content with Slot:</h4>
          <pre
            class="bg-muted p-4 rounded-md text-sm overflow-x-auto"
          ><code>&lt;SelectChipsValue :options="options"&gt;
  &lt;template #chip="{ option, label }"&gt;
    &lt;div class="flex items-center gap-1"&gt;
      &lt;span&gt;{{ option?.emoji }}&lt;/span&gt;
      &lt;span class="font-medium"&gt;{{ label }}&lt;/span&gt;
      &lt;span class="text-xs text-muted-foreground"&gt;
        ({{ option?.category }})
      &lt;/span&gt;
    &lt;/div&gt;
  &lt;/template&gt;
&lt;/SelectChipsValue&gt;</code></pre>
        </div>

        <div class="space-y-2">
          <h4 class="font-medium">6. With Display Limit:</h4>
          <pre class="bg-muted p-4 rounded-md text-sm overflow-x-auto"><code>&lt;SelectChipsValue
  :options="options"
  :max-display="3"  // Show max 3 chips, then "+X more"
/&gt;</code></pre>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
