<script setup lang="ts">
import { ref } from 'vue'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectChipsValue,
} from '@/components/ui/select'
import { Label } from '@/components/ui/label'

// Demo data
const fruits = [
  { value: 'apple', label: 'Apple' },
  { value: 'banana', label: 'Banana' },
  { value: 'orange', label: 'Orange' },
  { value: 'grape', label: 'Grape' },
  { value: 'strawberry', label: 'Strawberry' },
  { value: 'mango', label: 'Mango' },
  { value: 'pineapple', label: 'Pineapple' },
  { value: 'kiwi', label: 'Kiwi' },
  { value: 'watermelon', label: 'Watermelon' },
  { value: 'blueberry', label: 'Blueberry' },
]

// Reactive values
const singleValue = ref('')
const multipleValues = ref([])
const chipsValues = ref([])
const chipsWithLimitValues = ref([])
</script>

<template>
  <div class="container mx-auto p-6 space-y-8">
    <div class="space-y-2">
      <h1 class="text-3xl font-bold">Select Components Demo</h1>
      <p class="text-muted-foreground">
        Demonstrating the enhanced select component with chips variant for multiple selection.
      </p>
    </div>

    <div class="grid gap-6 md:grid-cols-2">
      <!-- Single Selection -->
      <Card>
        <CardHeader>
          <CardTitle>Single Selection</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-2">
            <Label>Choose a fruit</Label>
            <Select v-model="singleValue">
              <SelectTrigger>
                <SelectValue placeholder="Select a fruit..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="fruit in fruits" :key="fruit.value" :value="fruit.value">
                  {{ fruit.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div class="text-sm text-muted-foreground">
            Selected: {{ singleValue || 'None' }}
          </div>
        </CardContent>
      </Card>

      <!-- Multiple Selection (Default) -->
      <Card>
        <CardHeader>
          <CardTitle>Multiple Selection (Default)</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-2">
            <Label>Choose multiple fruits</Label>
            <Select v-model="multipleValues" multiple>
              <SelectTrigger>
                <SelectValue placeholder="Select fruits..." />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="fruit in fruits" :key="fruit.value" :value="fruit.value">
                  {{ fruit.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div class="text-sm text-muted-foreground">
            Selected: {{ multipleValues.length ? multipleValues.join(', ') : 'None' }}
          </div>
        </CardContent>
      </Card>

      <!-- Multiple Selection with Chips -->
      <Card>
        <CardHeader>
          <CardTitle>Multiple Selection with Chips</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-2">
            <Label>Choose multiple fruits (chips variant)</Label>
            <Select v-model="chipsValues" multiple>
              <SelectTrigger variant="chips">
                <SelectChipsValue 
                  placeholder="Select fruits..." 
                  :options="fruits"
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="fruit in fruits" :key="fruit.value" :value="fruit.value">
                  {{ fruit.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div class="text-sm text-muted-foreground">
            Selected: {{ chipsValues.length ? chipsValues.join(', ') : 'None' }}
          </div>
        </CardContent>
      </Card>

      <!-- Multiple Selection with Chips (Limited Display) -->
      <Card>
        <CardHeader>
          <CardTitle>Chips with Display Limit</CardTitle>
        </CardHeader>
        <CardContent class="space-y-4">
          <div class="space-y-2">
            <Label>Choose multiple fruits (max 3 chips shown)</Label>
            <Select v-model="chipsWithLimitValues" multiple>
              <SelectTrigger variant="chips">
                <SelectChipsValue 
                  placeholder="Select fruits..." 
                  :options="fruits"
                  :max-display="3"
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem v-for="fruit in fruits" :key="fruit.value" :value="fruit.value">
                  {{ fruit.label }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div class="text-sm text-muted-foreground">
            Selected: {{ chipsWithLimitValues.length ? chipsWithLimitValues.join(', ') : 'None' }}
          </div>
        </CardContent>
      </Card>
    </div>

    <!-- Code Examples -->
    <Card>
      <CardHeader>
        <CardTitle>Usage Examples</CardTitle>
      </CardHeader>
      <CardContent class="space-y-4">
        <div class="space-y-2">
          <h4 class="font-medium">Basic Chips Usage:</h4>
          <pre class="bg-muted p-4 rounded-md text-sm overflow-x-auto"><code>&lt;Select v-model="selectedValues" multiple&gt;
  &lt;SelectTrigger variant="chips"&gt;
    &lt;SelectChipsValue 
      placeholder="Select options..." 
      :options="options"
    /&gt;
  &lt;/SelectTrigger&gt;
  &lt;SelectContent&gt;
    &lt;SelectItem v-for="option in options" :value="option.value"&gt;
      {{ option.label }}
    &lt;/SelectItem&gt;
  &lt;/SelectContent&gt;
&lt;/Select&gt;</code></pre>
        </div>
        
        <div class="space-y-2">
          <h4 class="font-medium">With Display Limit:</h4>
          <pre class="bg-muted p-4 rounded-md text-sm overflow-x-auto"><code>&lt;SelectChipsValue 
  placeholder="Select options..." 
  :options="options"
  :max-display="3"
/&gt;</code></pre>
        </div>
      </CardContent>
    </Card>
  </div>
</template>
