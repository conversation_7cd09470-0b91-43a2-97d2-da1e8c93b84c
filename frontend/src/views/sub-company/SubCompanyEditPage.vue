<script setup lang="ts">
import { ref, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { toast } from 'vue-sonner'
import { useForm } from 'vee-validate'
import { toTypedSchema } from '@vee-validate/zod'
import * as z from 'zod'
import { useStateList } from '@/composables/useStateList'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { FormControl, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { ArrowLeft, Loader2 } from 'lucide-vue-next'
import { vMaska } from 'maska/vue'
import Alert from '@/components/ui/alert/Alert.vue'
import AlertDescription from '@/components/ui/alert/AlertDescription.vue'
import { useSubCompanyStore, useTreatmentStore, type SubCompanyPayload } from '@/stores'
import { storeToRefs } from 'pinia'

const subCompanyStore = useSubCompanyStore()
const {
  selectedSubCompany,
  isLoadingDetails,
  isUpdating,
  error: storeError,
  fieldErrors,
} = storeToRefs(subCompanyStore)
const { getSubCompanyById, updateSubCompany } = subCompanyStore
const treatmentStore = useTreatmentStore()
const { treatmentOptions, isLoadingTreatmentOptions } = storeToRefs(treatmentStore)
const { fetchTreatmentOptions } = treatmentStore
const { states, isLoadingStates, error: statesError } = useStateList()

const formError = ref('')
const route = useRoute()
const router = useRouter()
const companyId = route.params.company_id as string

const formData = ref<SubCompanyPayload>({
  first_name: '',
  last_name: '',
  email: '',
  phone_number: '',
  company_name: '',
  company_id: '',
  company_website_url: '',
  beluga_sub_company: '',
  // pharmacy_id: '',
  visit_prefix: '',
  visit_webhook_url: '',
  pharmacy_webhook_url: '',
  treatment_ids: [],
  address_line_1: '',
  address_line_2: undefined,
  city: '',
  state: '',
  country: 'USA',
  zipcode: '',
})

async function fetchSubCompanyDetails() {
  if (isLoadingDetails.value) return

  if (!companyId) return router.push({ name: 'sub-companies' })

  const result = await getSubCompanyById(companyId)

  if (result) {
    formData.value = {
      first_name: selectedSubCompany.value?.first_name || '',
      last_name: selectedSubCompany.value?.last_name || '',
      email: selectedSubCompany.value?.email || '',
      phone_number: selectedSubCompany.value?.phone_number || '',
      company_name: selectedSubCompany.value?.company_name || '',
      company_id: selectedSubCompany.value?.company_id || '',
      company_website_url: selectedSubCompany.value?.company_website_url || '',
      beluga_sub_company: selectedSubCompany.value?.beluga_sub_company || '',
      // pharmacy_id: selectedSubCompany.value?.pharmacy_id || '',
      visit_prefix: selectedSubCompany.value?.visit_prefix || '',
      visit_webhook_url: selectedSubCompany.value?.visit_webhook_url || '',
      pharmacy_webhook_url: selectedSubCompany.value?.pharmacy_webhook_url || '',
      treatment_ids: selectedSubCompany.value?.treatment_ids || [],
      address_line_1: selectedSubCompany.value?.address_line_1 || '',
      address_line_2: selectedSubCompany.value?.address_line_2 || undefined,
      city: selectedSubCompany.value?.city || '',
      state: selectedSubCompany.value?.state || '',
      country: selectedSubCompany.value?.country || 'USA',
      zipcode: selectedSubCompany.value?.zipcode || '',
    }
  } else {
    toast.error(storeError.value || 'Failed to fetch sub-company details')
    router.push({ name: 'sub-companies' })
  }
}

const formSchema = toTypedSchema(
  z.object({
    first_name: z.string().min(1, 'First name is required'),
    last_name: z.string().min(1, 'Last name is required'),
    email: z.string().email('Please enter a valid email address'),
    phone_number: z.string().min(1, 'Phone number is required'),
    company_name: z.string().min(1, 'Company name is required'),
    company_id: z.string().min(1, 'Company ID is required'),
    company_website_url: z.string().url('Please enter a valid URL'),
    beluga_sub_company: z.string().min(1, 'Beluga sub company is required'),
    // pharmacy_id: z.string().min(1, 'Pharmacy ID is required'),
    visit_prefix: z.string().min(1, 'Visit prefix is required').max(6),
    visit_webhook_url: z.string().url('Please enter a valid webhook URL'),
    pharmacy_webhook_url: z.string().url('Please enter a valid pharmacy webhook URL'),
    treatment_ids: z.array(z.string()).min(1, 'Treatment is required'),
    address_line_1: z.string().min(1, 'Address line 1 is required'),
    address_line_2: z.string().optional().nullable(),
    city: z.string().min(1, 'City is required'),
    state: z.string().min(2, 'Please select a state'),
    country: z.string().min(1, 'Country is required'),
    zipcode: z.string().min(1, 'Zipcode is required'),
  }),
)

const form = useForm({
  validationSchema: formSchema,
  initialValues: formData.value,
})

watch(
  () => formData.value,
  () => {
    form.setValues(formData.value)
  },
)

const onSubmit = form.handleSubmit(async (values: unknown) => {
  if (isUpdating.value) return
  formError.value = ''

  const formValues = values as SubCompanyPayload
  const result = await updateSubCompany({
    ...formValues,
    phone_number: formValues.phone_number.replace(/[^0-9]/g, ''),
    id: companyId,
  })

  if (result) {
    router.push({ name: 'sub-companies' })
  } else if (fieldErrors.value) {
    const errors = fieldErrors.value
    Object.entries(errors).forEach(([field, messages]) => {
      if (Array.isArray(messages) && messages.length > 0) {
        form.setFieldError(field as keyof SubCompanyPayload, messages[0])
      }
    })
  } else {
    formError.value = storeError.value || 'Failed to update sub company. Please try again.'
  }
})

onMounted(async () => {
  await Promise.all([fetchTreatmentOptions(), fetchSubCompanyDetails()])
})
</script>

<template>
  <div class="w-full">
    <div class="flex items-center gap-3">
      <div class="mb-4">
        <Button variant="ghost" size="sm" @click="router.back()">
          <ArrowLeft class="h-4 w-4" />
          <span>Back</span>
        </Button>
      </div>
      <h1 class="text-2xl font-bold mb-4">Edit Sub-Company</h1>
    </div>

    <Card>
      <CardContent class="pt-6">
        <!-- Loading -->
        <div v-if="isLoadingDetails" class="text-center py-8">
          <Loader2 class="animate-spin h-8 w-8 text-gray-500 mx-auto" />
        </div>

        <!-- Form -->
        <form v-else class="space-y-10" @submit="onSubmit">
          <!-- Form Error Message -->
          <Alert v-if="formError" variant="destructive">
            <AlertDescription>{{ formError }}</AlertDescription>
          </Alert>

          <!-- Contact Information Section -->
          <div>
            <h2 class="text-lg font-medium mb-4">Contact Information</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-start">
              <!-- First Name -->
              <FormField name="first_name" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>First Name</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter first name"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Last Name -->
              <FormField name="last_name" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Last Name</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter last name"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Email -->
              <FormField name="email" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Email</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      type="email"
                      placeholder="Enter email address"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Phone Number -->
              <FormField name="phone_number" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Phone Number</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      v-maska="'(###) ###-####'"
                      placeholder="Enter phone number"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            </div>
          </div>

          <!-- Company Information Section -->
          <div>
            <h2 class="text-lg font-medium mb-4">Company Information</h2>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-start">
              <!-- Company Name -->
              <FormField name="company_name" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Company Name</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter company name"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Company ID -->
              <FormField name="company_id" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Company ID</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter company ID"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Company Website URL -->
              <FormField name="company_website_url" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Company Website URL</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      type="url"
                      placeholder="https://example.com"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Beluga Sub Company -->
              <FormField name="beluga_sub_company" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Beluga Sub Company</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter beluga sub company"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Pharmacy ID -->
              <!-- <FormField name="pharmacy_id" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Pharmacy ID</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter pharmacy ID"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField> -->

              <!-- Visit Prefix -->
              <FormField name="visit_prefix" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Visit Prefix</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter visit prefix"
                      :aria-invalid="!!errorMessage"
                      maxlength="6"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Treatment -->
              <FormField name="treatment_ids" v-slot="{ field, errorMessage }">
                <FormItem>
                  <FormLabel>Treatments (Choose one or more)</FormLabel>
                  <FormControl>
                    <Select
                      :model-value="field.value"
                      @update:model-value="field.onChange($event)"
                      :disabled="isLoadingTreatmentOptions"
                      multiple
                    >
                      <SelectTrigger :aria-invalid="!!errorMessage" class="w-full">
                        <SelectValue placeholder="Select treatments" />
                      </SelectTrigger>
                      <SelectContent>
                        <div v-if="isLoadingTreatmentOptions" class="p-2 text-center text-sm">
                          Loading treatments...
                        </div>
                        <div
                          v-else-if="treatmentOptions.length === 0"
                          class="p-2 text-center text-sm"
                        >
                          No treatments available
                        </div>
                        <SelectItem
                          v-for="treatment in treatmentOptions"
                          :key="treatment.id"
                          :value="treatment.id"
                        >
                          {{ treatment.treatment_name }} ({{ treatment.beluga_visit_type }})
                        </SelectItem>
                      </SelectContent>
                    </Select>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            </div>
          </div>

          <!-- Webhook -->
          <div>
            <h2 class="text-lg font-medium mb-4">Internal Communication</h2>
            <div class="grid grid-cols-1 gap-4 items-start">
              <!-- Webhook URL -->
              <FormField
                name="visit_webhook_url"
                v-slot="{ componentField, errorMessage }"
                class="col-span-2"
              >
                <FormItem>
                  <FormLabel>Visit Webhook URL</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter webhook URL"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Pharmacy Webhook URL -->
              <FormField
                name="pharmacy_webhook_url"
                v-slot="{ componentField, errorMessage }"
                class="col-span-2"
              >
                <FormItem>
                  <FormLabel>Pharmacy Webhook URL</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter pharmacy webhook URL"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>
            </div>
          </div>

          <!-- Address Section -->
          <div>
            <h2 class="text-lg font-medium mb-4">Address</h2>
            <div class="grid grid-cols-1 gap-4 items-start">
              <!-- Address Line 1 -->
              <FormField name="address_line_1" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Address Line 1</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter address line 1"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <!-- Address Line 2 -->
              <FormField name="address_line_2" v-slot="{ componentField, errorMessage }">
                <FormItem>
                  <FormLabel>Address Line 2 (Optional)</FormLabel>
                  <FormControl>
                    <Input
                      v-bind="componentField"
                      placeholder="Enter address line 2"
                      :aria-invalid="!!errorMessage"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              </FormField>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 items-start">
                <!-- City -->
                <FormField name="city" v-slot="{ componentField, errorMessage }">
                  <FormItem>
                    <FormLabel>City</FormLabel>
                    <FormControl>
                      <Input
                        v-bind="componentField"
                        placeholder="Enter city"
                        :aria-invalid="!!errorMessage"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>

                <!-- State -->
                <FormField name="state" v-slot="{ field, errorMessage }">
                  <FormItem>
                    <FormLabel>State</FormLabel>
                    <FormControl>
                      <Select
                        :model-value="field.value"
                        @update:model-value="field.onChange($event)"
                        :disabled="isLoadingStates"
                      >
                        <SelectTrigger :aria-invalid="!!errorMessage" class="w-full">
                          <SelectValue placeholder="Select a state" />
                        </SelectTrigger>
                        <SelectContent>
                          <div v-if="isLoadingStates" class="p-2 text-center text-sm">
                            Loading states...
                          </div>
                          <div
                            v-else-if="statesError"
                            class="p-2 text-center text-sm text-destructive-foreground"
                          >
                            {{ statesError }}
                          </div>
                          <div v-else-if="states.length === 0" class="p-2 text-center text-sm">
                            No states available
                          </div>
                          <SelectItem v-for="state in states" :key="state.code" :value="state.code">
                            {{ state.name }}
                          </SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>

                <!-- Country -->
                <FormField name="country" v-slot="{ field, errorMessage }">
                  <FormItem>
                    <FormLabel>Country</FormLabel>
                    <FormControl>
                      <Select
                        :model-value="field.value"
                        @update:model-value="field.onChange($event)"
                      >
                        <SelectTrigger :aria-invalid="!!errorMessage" class="w-full">
                          <SelectValue placeholder="Select a country" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="USA">United States</SelectItem>
                        </SelectContent>
                      </Select>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>

                <!-- Zipcode -->
                <FormField name="zipcode" v-slot="{ componentField, errorMessage }">
                  <FormItem>
                    <FormLabel>Zipcode</FormLabel>
                    <FormControl>
                      <Input
                        v-bind="componentField"
                        v-maska="'#####'"
                        placeholder="Enter zipcode"
                        :aria-invalid="!!errorMessage"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                </FormField>
              </div>
            </div>
          </div>

          <!-- Submit Button -->
          <div class="flex justify-end gap-3">
            <Button variant="outline" type="button" :disabled="isUpdating" @click="router.back()">
              Cancel
            </Button>
            <Button type="submit" :disabled="isUpdating">
              {{ isUpdating ? 'Updating...' : 'Update' }}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  </div>
</template>
