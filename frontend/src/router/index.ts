import { createRouter, createWebHistory } from 'vue-router'
import MainLayout from '../layouts/MainLayout.vue'
import { defineMiddleware } from '@/middleware'
import { auth, guest } from '@/middleware/auth'

const routes = [
  {
    path: '/',
    component: MainLayout,
    beforeEnter: defineMiddleware(auth),
    children: [
      {
        path: '',
        name: 'dashboard',
        component: () => import('@/views/dashboard/DashboardPage.vue'),
        meta: {
          title: 'Dashboard',
        },
      },

      // Sub Companies
      {
        path: 'sub-companies',
        name: 'sub-companies',
        component: () => import('@/views/sub-company/SubCompanyListPage.vue'),
        meta: {
          title: 'Sub Companies',
        },
      },
      {
        path: 'sub-companies/add',
        name: 'add-sub-company',
        component: () => import('@/views/sub-company/SubCompanyAddPage.vue'),
        meta: {
          title: 'Add Sub Company',
          parent: 'Sub Companies',
          parentRouteName: 'sub-companies',
        },
      },
      {
        path: 'sub-companies/edit/:company_id',
        name: 'edit-sub-company',
        component: () => import('@/views/sub-company/SubCompanyEditPage.vue'),
        meta: {
          title: 'Edit Sub Company',
          parent: 'Sub Companies',
          parentRouteName: 'sub-companies',
        },
      },
      {
        path: 'sub-companies/:company_id',
        name: 'view-sub-company',
        component: () => import('@/views/sub-company/SubCompanyDetailPage.vue'),
        meta: {
          title: 'Sub Company Detail',
          parent: 'Sub Companies',
          parentRouteName: 'sub-companies',
        },
      },

      // Treatments
      {
        path: 'treatments',
        name: 'treatments',
        component: () => import('@/views/treatments/TreatmentListPage.vue'),
        meta: {
          title: 'Treatments',
        },
      },
      {
        path: 'treatments/add',
        name: 'add-treatment',
        component: () => import('@/views/treatments/TreatmentAddPage.vue'),
        meta: {
          title: 'Add Treatment',
          parent: 'Treatments',
          parentRouteName: 'treatments',
        },
      },
      {
        path: 'treatments/edit/:treatment_id',
        name: 'edit-treatment',
        component: () => import('@/views/treatments/TreatmentEditPage.vue'),
        meta: {
          title: 'Edit Treatment',
          parent: 'Treatments',
          parentRouteName: 'treatments',
        },
      },

      // Pharmacies
      {
        path: 'pharmacies',
        name: 'pharmacies',
        component: () => import('@/views/pharmacies/PharmacyListPage.vue'),
        meta: {
          title: 'Pharmacies',
        },
      },
      {
        path: 'pharmacies/add',
        name: 'add-pharmacy',
        component: () => import('@/views/pharmacies/PharmacyAddPage.vue'),
        meta: {
          title: 'Add Pharmacy',
          parent: 'Pharmacies',
          parentRouteName: 'pharmacies',
        },
      },
      {
        path: 'pharmacies/edit/:pharmacy_id',
        name: 'edit-pharmacy',
        component: () => import('@/views/pharmacies/PharmacyEditPage.vue'),
        meta: {
          title: 'Edit Pharmacy',
          parent: 'Pharmacies',
          parentRouteName: 'pharmacies',
        },
      },

      // Beluga Visits
      {
        path: 'beluga-visits',
        name: 'beluga-visits',
        component: () => import('@/views/beluga/BelugaVisitListPage.vue'),
        meta: {
          title: 'Beluga Visits',
        },
      },
      {
        path: 'beluga-visits/:visit_id',
        name: 'beluga-visit-detail',
        component: () => import('@/views/beluga/BelugaVisitDetailPage.vue'),
        meta: {
          title: 'Beluga Visit Detail',
          parent: 'Beluga Visits',
          parentRouteName: 'beluga-visits',
        },
      },

      // Account
      {
        path: 'account',
        name: 'account',
        component: () => import('@/views/account/AccountPage.vue'),
        meta: {
          title: 'Account Settings',
        },
      },
    ],
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/views/auth/LoginPage.vue'),
    beforeEnter: defineMiddleware(guest),
    meta: {
      title: 'Login',
    },
  },

  // Catch-all 404 route
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('@/views/error/Error404Page.vue'),
    meta: {
      title: '404 Not Found',
    },
  },
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else if (to.hash) {
      return { el: to.hash, behavior: 'smooth' }
    } else if (from.path === to.path) {
      return { top: 0 }
    } else {
      return { top: 0 }
    }
  },
})

router.beforeEach((to, from, next) => {
  document.title = `${to.meta.title} - WhiteLabelRx Admin` || 'WhiteLabelRx Admin'
  next()
})

export default router
